import requests
import json
import pygame
import io
from typing import Optional

class VoiceVoxClient:
    def __init__(self, voicevox_url: str = "http://localhost:50021"):
        """
        Initialize VOICEVOX client
        
        Args:
            voicevox_url: URL of the VOICEVOX engine (default: localhost:50021)
        """
        self.base_url = voicevox_url
        self.speaker_id = 1  # Default speaker (可不子)
        
        # Initialize pygame mixer for audio playback
        pygame.mixer.init()
        
    def check_voicevox_status(self) -> bool:
        """Check if VOICEVOX engine is running"""
        try:
            response = requests.get(f"{self.base_url}/version", timeout=5)
            if response.status_code == 200:
                version_info = response.json()
                print(f"VOICEVOX Engine connected - Version: {version_info}")
                return True
            return False
        except Exception as e:
            print(f"VOICEVOX Engine not available: {e}")
            return False
    
    def get_speakers(self) -> Optional[list]:
        """Get available speakers from VOICEVOX"""
        try:
            response = requests.get(f"{self.base_url}/speakers")
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            print(f"Error getting speakers: {e}")
            return None
    
    def text_to_speech(self, text: str, speaker_id: Optional[int] = None) -> bool:
        """
        Convert text to speech and play it
        
        Args:
            text: Text to convert to speech
            speaker_id: Speaker ID to use (default: self.speaker_id)
            
        Returns:
            True if successful, False otherwise
        """
        if speaker_id is None:
            speaker_id = self.speaker_id
            
        try:
            # Step 1: Create audio query
            query_response = requests.post(
                f"{self.base_url}/audio_query",
                params={"text": text, "speaker": speaker_id},
                timeout=10
            )
            
            if query_response.status_code != 200:
                print(f"Error creating audio query: {query_response.status_code}")
                return False
            
            audio_query = query_response.json()
            
            # Step 2: Generate audio
            synthesis_response = requests.post(
                f"{self.base_url}/synthesis",
                params={"speaker": speaker_id},
                json=audio_query,
                timeout=30
            )
            
            if synthesis_response.status_code != 200:
                print(f"Error generating audio: {synthesis_response.status_code}")
                return False
            
            # Step 3: Play audio
            audio_data = synthesis_response.content
            audio_file = io.BytesIO(audio_data)
            
            pygame.mixer.music.load(audio_file)
            pygame.mixer.music.play()
            
            # Wait for audio to finish playing
            while pygame.mixer.music.get_busy():
                pygame.time.wait(100)
            
            return True
            
        except Exception as e:
            print(f"Error in text-to-speech: {e}")
            return False
    
    def set_speaker(self, speaker_id: int):
        """Set the default speaker ID"""
        self.speaker_id = speaker_id
        print(f"Speaker set to ID: {speaker_id}")
