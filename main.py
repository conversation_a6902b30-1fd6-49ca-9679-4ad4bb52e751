import speech_recognition as sr
import requests
import time
from typing import Optional, List, Dict
from voicevox_client import VoiceVoxClient

class AIVtuber:
    def __init__(self):
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        self.is_listening = False

        # Conversation history
        self.conversation_history: List[Dict[str, str]] = []
        self.max_history_length = 10  # Keep last 10 exchanges

        # VOICEVOX client
        self.voicevox = VoiceVoxClient()
        self.use_voice_output = self.voicevox.check_voicevox_status()

        if self.use_voice_output:
            print("✓ VOICEVOX engine detected - Voice output enabled!")
            # List available speakers
            speakers = self.voicevox.get_speakers()
            if speakers:
                print("Available speakers:")
                for speaker in speakers[:5]:  # Show first 5 speakers
                    print(f"  ID {speaker.get('speaker_uuid', 'N/A')}: {speaker.get('name', 'Unknown')}")
        else:
            print("⚠ VOICEVOX engine not found - Text output only")

        # Adjust for ambient noise
        print("Adjusting for ambient noise... Please wait.")
        with self.microphone as source:
            self.recognizer.adjust_for_ambient_noise(source)
        print("Ready to listen!")

    def listen_for_speech(self) -> Optional[str]:
        """Listen for speech from microphone and return transcribed text"""
        try:
            with self.microphone as source:
                print("Listening... Speak now!")
                # Listen for audio with timeout
                audio = self.recognizer.listen(source, timeout=5, phrase_time_limit=10)

            print("Processing speech...")
            # Use Google's speech recognition (free tier)
            text = self.recognizer.recognize_google(audio)
            print(f"You said: {text}")
            return text

        except sr.WaitTimeoutError:
            print("No speech detected within timeout period")
            return None
        except sr.UnknownValueError:
            print("Could not understand the audio")
            return None
        except sr.RequestError as e:
            print(f"Error with speech recognition service: {e}")
            return None

    def add_to_history(self, user_input: str, ai_response: str):
        """Add exchange to conversation history"""
        self.conversation_history.append({
            "user": user_input,
            "assistant": ai_response,
            "timestamp": time.time()
        })

        # Keep only recent history
        if len(self.conversation_history) > self.max_history_length:
            self.conversation_history = self.conversation_history[-self.max_history_length:]

    def get_context_prompt(self, user_input: str) -> str:
        """Create a prompt with conversation history context"""
        base_prompt = "You are a friendly AI vtuber assistant named Ai-chan. Respond in a cheerful, engaging way like a real vtuber would - be enthusiastic, friendly, and helpful. Keep responses conversational and not too long."

        if not self.conversation_history:
            return f"{base_prompt}\n\nUser said: {user_input}\n\nRespond as Ai-chan:"

        # Build context from recent conversation
        context = f"{base_prompt}\n\nRecent conversation:\n"
        for exchange in self.conversation_history[-3:]:  # Last 3 exchanges for context
            context += f"User: {exchange['user']}\nAi-chan: {exchange['assistant']}\n\n"

        context += f"User: {user_input}\nAi-chan:"
        return context

    def generate_response(self, user_input: str) -> str:
        """Generate AI response using Pollinations API with conversation history"""
        try:
            # Using Pollinations text generation API
            url = "https://text.pollinations.ai/"

            # Create a prompt with conversation context
            prompt = self.get_context_prompt(user_input)

            response = requests.post(url,
                                   json={"messages": [{"role": "user", "content": prompt}]},
                                   headers={"Content-Type": "application/json"},
                                   timeout=15)

            if response.status_code == 200:
                ai_response = response.text.strip()
                print(f"AI Response: {ai_response}")

                # Add to conversation history
                self.add_to_history(user_input, ai_response)

                return ai_response
            else:
                print(f"Error generating response: {response.status_code}")
                return "Sorry, I couldn't generate a response right now!"

        except Exception as e:
            print(f"Error generating response: {e}")
            return "Sorry, I encountered an error while thinking of a response!"

    def speak_response(self, text: str):
        """Speak the response using VOICEVOX if available"""
        if self.use_voice_output:
            print("🔊 Speaking response...")
            success = self.voicevox.text_to_speech(text)
            if not success:
                print("⚠ Voice output failed, falling back to text only")
        else:
            print("📝 Voice output not available")

    def show_conversation_history(self):
        """Display recent conversation history"""
        if not self.conversation_history:
            print("No conversation history yet.")
            return

        print("\n📚 Recent Conversation History:")
        print("=" * 40)
        for i, exchange in enumerate(self.conversation_history[-5:], 1):
            print(f"{i}. User: {exchange['user']}")
            print(f"   Ai-chan: {exchange['assistant']}")
            print("-" * 30)

    def run_conversation_loop(self):
        """Main conversation loop"""
        print("AI Vtuber (Ai-chan) is ready! Press Ctrl+C to stop.")
        if self.use_voice_output:
            print("🔊 Voice output enabled with VOICEVOX")
        print("Say something to start the conversation...")

        try:
            while True:
                # Listen for user input
                user_speech = self.listen_for_speech()

                if user_speech:
                    # Check for special commands
                    user_lower = user_speech.lower()
                    if user_lower in ["show history", "history", "conversation history"]:
                        self.show_conversation_history()
                        continue
                    elif user_lower in ["clear history", "reset history"]:
                        self.conversation_history.clear()
                        response = "Conversation history cleared! Let's start fresh!"
                        print(f"\n🤖 Ai-chan: {response}\n")
                        self.speak_response(response)
                        continue
                    elif user_lower in ["goodbye", "bye", "exit", "quit"]:
                        farewell = "またね！バイバイ！"
                        print(f"\n🤖 Ai-chan: {farewell}\n")
                        self.speak_response(farewell)
                        break

                    # Generate AI response
                    ai_response = self.generate_response(user_speech)
                    print(f"\n🤖 Ai-chan: {ai_response}\n")

                    # Speak the response if VOICEVOX is available
                    self.speak_response(ai_response)

                    print("-" * 50)

                # Small delay before listening again
                time.sleep(1)

        except KeyboardInterrupt:
            print("\nStopping AI Vtuber. Goodbye!")
            if self.use_voice_output:
                # Say goodbye with voice
                self.voicevox.text_to_speech("またね！バイバイ！")

def main():
    vtuber = AIVtuber()
    vtuber.run_conversation_loop()

if __name__ == "__main__":
    main()
